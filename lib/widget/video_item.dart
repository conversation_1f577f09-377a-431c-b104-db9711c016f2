import 'dart:io';

import 'package:fldanplay/utils/format.dart';
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:path_provider/path_provider.dart';
import '../model/history.dart';

class FileImageEx extends FileImage {
  late final int fileSize;
  FileImageEx(File file, {double scale = 1.0}) : super(file, scale: scale) {
    fileSize = file.lengthSync();
  }

  @override
  // ignore: hash_and_equals
  bool operator ==(Object other) {
    if (other.runtimeType != runtimeType) return false;
    return other is FileImageEx &&
        other.file.path == file.path &&
        other.scale == scale &&
        other.fileSize == fileSize;
  }
}

class VideoItem extends StatefulWidget with FItemMixin {
  final History? history;
  final String name;
  final void Function() onPress;
  final void Function()? onLongPress;
  const VideoItem({
    super.key,
    required this.history,
    required this.name,
    required this.onPress,
    this.onLongPress,
  });
  @override
  State<VideoItem> createState() => _VideoItemState();
}

class _VideoItemState extends State<VideoItem> {
  late final Future<Widget> _perfixFuture;

  @override
  void initState() {
    super.initState();
    _perfixFuture = _buildPerfix(widget.history);
  }

  Future<Widget> _buildPerfix(History? history) async {
    if (history != null) {
      final directory = await getApplicationDocumentsDirectory();
      return LayoutBuilder(
        builder: (context, boxConstraints) {
          final double maxWidth = boxConstraints.maxWidth;
          final double maxHeight = boxConstraints.maxHeight;
          return Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(4),
                child: Image(
                  image: FileImageEx(
                    File('${directory.path}/screenshots/${history.uniqueKey}'),
                  ),
                  fit: BoxFit.fitHeight,
                  width: maxWidth,
                  height: maxHeight,
                  errorBuilder: (context, error, stackTrace) {
                    return const Icon(FIcons.play, size: 50);
                  },
                ),
              ),
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Colors.black.withAlpha(128),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(4),
                    ),
                  ),
                  child: Text(
                    formatTime(history.position, history.duration),
                    style: const TextStyle(color: Colors.white, fontSize: 11),
                  ),
                ),
              ),
            ],
          );
        },
      );
    }
    return const Icon(FIcons.play, size: 50);
  }

  @override
  Widget build(BuildContext context) {
    double progress = 0;
    int progressPercent = 0;
    String lastWatchTime = '';
    if (widget.history != null) {
      progress =
          widget.history!.duration > 0
              ? (widget.history!.position / widget.history!.duration).clamp(
                0.0,
                1.0,
              )
              : 0.0;
      progressPercent = (progress * 100).round();
      lastWatchTime = formatLastWatchTime(widget.history!.updateTime);
    }
    final subtitleStyle = context.theme.itemStyle.contentStyle.subtitleTextStyle
        .resolve({});
    return FItem(
      prefix: SizedBox(
        width: 95,
        height: 65,
        child: FutureBuilder(
          future: _perfixFuture,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const CircularProgressIndicator();
            }
            if (snapshot.hasData) {
              return snapshot.data!;
            }
            return const Icon(FIcons.file, size: 50);
          },
        ),
      ),
      title: ConstrainedBox(
        constraints: const BoxConstraints(minHeight: 65),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            FTooltip(
              tipBuilder:
                  (context, controller) => Container(
                    constraints: BoxConstraints(
                      maxWidth: MediaQuery.of(context).size.width - 50,
                    ),
                    child: Text(widget.name),
                  ),
              child: Text(
                widget.name,
                style: context.theme.typography.base,
                maxLines: 2,
              ),
            ),
            const SizedBox(height: 4),
            widget.history != null
                ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    widget.history!.subtitle == null
                        ? const SizedBox()
                        : Text(widget.history!.subtitle!, style: subtitleStyle),
                    FProgress(
                      value: progress,
                      duration: Duration.zero,
                      style:
                          (style) => style.copyWith(
                            constraints: style.constraints.copyWith(
                              minHeight: 4,
                              maxHeight: 4,
                            ),
                          ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(style: subtitleStyle, lastWatchTime),
                        Text(style: subtitleStyle, '$progressPercent%'),
                      ],
                    ),
                  ],
                )
                : Text(style: subtitleStyle, '未观看'),
          ],
        ),
      ),
      onPress: widget.onPress,
      onLongPress: widget.onLongPress,
    );
  }
}
