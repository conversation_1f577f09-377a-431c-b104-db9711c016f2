import 'package:fldanplay/model/history.dart';
import 'package:fldanplay/model/stream_media.dart';
import 'package:fldanplay/model/video_info.dart';
import 'package:fldanplay/router.dart';
import 'package:fldanplay/service/history.dart';
import 'package:fldanplay/service/stream_media_explorer.dart';
import 'package:fldanplay/widget/network_image.dart';
import 'package:fldanplay/widget/video_item.dart';
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';

class StreamMediaDetailPage extends StatefulWidget {
  final MediaItem mediaItem;
  const StreamMediaDetailPage({super.key, required this.mediaItem});

  @override
  State<StreamMediaDetailPage> createState() => _StreamMediaDetailPageState();
}

class _StreamMediaDetailPageState extends State<StreamMediaDetailPage>
    with TickerProviderStateMixin {
  late final StreamMediaExplorerService _service;
  final _historyService = GetIt.I.get<HistoryService>();
  late TabController _tabController;
  MediaDetail? _mediaDetail;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _service = GetIt.I.get<StreamMediaExplorerService>();
    _tabController = TabController(length: 0, vsync: this);
    _loadMediaDetail();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadMediaDetail() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final detail = await _service.getMediaDetail(widget.mediaItem.id);

      setState(() {
        _mediaDetail = detail;
        _isLoading = false;

        // 重新创建TabController
        _tabController.dispose();
        _tabController = TabController(
          length: detail.seasons.length,
          vsync: this,
        );
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return <Widget>[
            SliverOverlapAbsorber(
              handle: NestedScrollView.sliverOverlapAbsorberHandleFor(context),
              sliver: SliverAppBar.medium(
                title: Text(widget.mediaItem.name),
                scrolledUnderElevation: 0,
                stretch: true,
                centerTitle: false,
                expandedHeight: 280 + kTextTabBarHeight + kToolbarHeight,
                toolbarHeight: kToolbarHeight,
                collapsedHeight:
                    kTextTabBarHeight +
                    kToolbarHeight +
                    MediaQuery.paddingOf(context).top,
                forceElevated: innerBoxIsScrolled,
                flexibleSpace: FlexibleSpaceBar(
                  collapseMode: CollapseMode.pin,
                  background: Stack(
                    children: [
                      SafeArea(
                        bottom: false,
                        child: Padding(
                          padding: const EdgeInsets.fromLTRB(
                            16,
                            kToolbarHeight,
                            16,
                            0,
                          ),
                          child: _buildMediaInfoWithLoading(),
                        ),
                      ),
                    ],
                  ),
                ),
                bottom: TabBar(
                  controller: _tabController,
                  isScrollable: true,
                  tabAlignment: TabAlignment.center,
                  dividerHeight: 0,
                  tabs:
                      _mediaDetail == null
                          ? []
                          : _mediaDetail!.seasons
                              .map((season) => Tab(text: season.name))
                              .toList(),
                ),
              ),
            ),
          ];
        },
        body: _buildTabContent(),
      ),
    );
  }

  Widget _buildTabContent() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('加载失败: $_error'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadMediaDetail,
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (_mediaDetail == null || _mediaDetail!.seasons.isEmpty) {
      return const Center(child: Text('暂无季度信息'));
    }

    return TabBarView(
      controller: _tabController,
      children:
          _mediaDetail!.seasons
              .map((season) => _buildSeasonView(season))
              .toList(),
    );
  }

  Widget _buildMediaInfoWithLoading() {
    final provider = _service.provider;

    return Container(
      height: 280,
      constraints: BoxConstraints(maxWidth: 950),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(bottom: 6),
            child: Text(
              widget.mediaItem.name,
              style: context.theme.typography.xl.copyWith(height: 1.2),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Flexible(
                  child: AspectRatio(
                    aspectRatio: 0.7,
                    child: LayoutBuilder(
                      builder: (context, boxConstraints) {
                        final double maxWidth = boxConstraints.maxWidth;
                        final double maxHeight = boxConstraints.maxHeight;
                        return Hero(
                          transitionOnUserGestures: true,
                          tag: widget.mediaItem.id,
                          child: NetworkImageWidget(
                            url: provider.getImageUrl(widget.mediaItem.id),
                            headers: provider.headers,
                            maxWidth: maxWidth,
                            maxHeight: maxHeight,
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSeasonView(SeasonInfo season) {
    if (season.episodes.isEmpty) {
      return const Center(child: Text('暂无集数'));
    }
    return Builder(
      builder: (BuildContext context) {
        return CustomScrollView(
          scrollBehavior: const ScrollBehavior().copyWith(scrollbars: false),
          slivers: <Widget>[
            SliverOverlapInjector(
              handle: NestedScrollView.sliverOverlapAbsorberHandleFor(context),
            ),
            SliverLayoutBuilder(
              builder: (context, constraints) {
                return SliverList.builder(
                  itemCount: season.episodes.length,
                  itemBuilder: (context, index) {
                    return _buildEpisodeItem(season.episodes[index]);
                  },
                );
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildEpisodeItem(EpisodeInfo episode) {
    return VideoItem(
      name: episode.name,
      history: _historyService.getHistoryByPath(episode.id),
      onPress: () async {
        try {
          final playbackUrl = await _service.getPlaybackUrl(episode.id);
          final videoInfo = VideoInfo(
            currentVideoPath: playbackUrl,
            virtualVideoPath: episode.id,
            historiesType: HistoriesType.streamMediaStorage,
            storageKey: _service.storage!.uniqueKey,
            name: episode.name,
            videoName: '${episode.seriesName} ${episode.indexNumber}',
            subtitle: '${episode.seriesName} ${episode.indexNumber}',
          );
          if (mounted) {
            final location = Uri(path: videoPlayerPath);
            context.push(location.toString(), extra: videoInfo);
          }
        } catch (e) {
          if (mounted) {
            showFToast(context: context, title: Text('播放失败: ${e.toString()}'));
          }
        }
      },
    );
  }
}
