import 'dart:async';
import 'package:battery_plus/battery_plus.dart';
import 'package:fldanplay/page/player/indicator.dart';
import 'package:fldanplay/utils/log.dart';
import 'package:flutter_volume_controller/flutter_volume_controller.dart';
import 'package:intl/intl.dart';
import 'package:screen_brightness/screen_brightness.dart';
import 'package:signals_flutter/signals_flutter.dart';

class PlayerUIState {
  final Signal<bool> showControls = Signal(true);
  final Battery _battery = Battery();
  final Signal<int> batteryLevel = Signal(0);
  final Signal<bool> batteryChange = Signal(false);
  final Signal<String> currentTime = Signal('');
  final Signal<bool> showProgressIndicator = Signal(false);
  final Signal<IndicatorType> activeIndicator = Signal(IndicatorType.none);
  final Signal<double> indicatorValue = Signal(0.0);
  final Signal<double> currentVolume = Signal(0.5);
  final Signal<double> currentBrightness = Signal(0.5);
  final Signal<String> progressIndicatorText = Signal('');

  Timer? _timeTimer;
  Timer? _hideControlsTimer;
  Timer? _hideIndicatorTimer;
  double? initialVolumeOnPan;
  double? initialBrightnessOnPan;
  Duration? initialPositionOnPan;

  Future<void> init() async {
    // 初始化音量和亮度控制
    await BrightnessVolumeService.initialize();
    currentVolume.value = BrightnessVolumeService.currentVolume;
    currentBrightness.value = BrightnessVolumeService.currentBrightness;

    _timeTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      currentTime.value = DateFormat('HH:mm').format(DateTime.now());
      _battery.batteryLevel.then((value) => batteryLevel.value = value);
      _battery.batteryState.then(
        (value) =>
            batteryChange.value = value == BatteryState.charging ? true : false,
      );
    });
  }

  /// 显示控制栏并设置自动隐藏
  void showControlsTemporarily() {
    _hideControlsTimer?.cancel();
    showControls.value = true;
    _hideControlsTimer = Timer(const Duration(seconds: 3), () {
      showControls.value = false;
    });
  }

  /// 更新控制栏显示状态
  void updateControlsVisibility(bool show) {
    _hideControlsTimer?.cancel();
    showControls.value = show;
  }

  /// 开始手势操作
  void startGesture({
    double? initialVolume,
    double? initialBrightness,
    Duration? initialPosition,
  }) {
    // 记录初始值
    initialVolumeOnPan = initialVolume;
    initialBrightnessOnPan = initialBrightness;
    initialPositionOnPan = initialPosition;
    showControls.value = false;
  }

  /// 结束手势操作
  void endGesture() {
    hideAllIndicators();
    initialVolumeOnPan = null;
    initialBrightnessOnPan = null;
    initialPositionOnPan = null;
  }

  /// 开始长按（倍速）
  void startLongPress(double speed) {
    showIndicator(IndicatorType.speed, speed, permanent: true);
  }

  /// 结束长按（倍速）
  void endLongPress() {
    hideIndicator();
  }

  /// 显示音量控制
  void setVolume(double volume) {
    currentVolume.value = volume;
    showIndicator(IndicatorType.volume, volume);
  }

  /// 显示亮度控制
  void setBrightness(double brightness) {
    currentBrightness.value = brightness;
    showIndicator(IndicatorType.brightness, brightness);
  }

  /// 显示进度指示器
  void setProgressIndicator(String text) {
    batch(() {
      progressIndicatorText.value = text;
      showProgressIndicator.value = true;
      hideIndicator();
    });
  }

  /// 隐藏所有控制指示器
  void hideAllIndicators() {
    batch(() {
      showProgressIndicator.value = false;
    });
  }

  /// 显示一个通用的指示器（如音量、亮度、速度）
  void showIndicator(
    IndicatorType type,
    double value, {
    bool permanent = false,
  }) {
    activeIndicator.value = type;
    indicatorValue.value = value;
    showProgressIndicator.value = false;

    _hideIndicatorTimer?.cancel();
    if (!permanent) {
      _hideIndicatorTimer = Timer(const Duration(seconds: 1), hideIndicator);
    }
  }

  /// 隐藏指示器
  void hideIndicator() {
    activeIndicator.value = IndicatorType.none;
  }

  /// 释放资源
  void dispose() {
    _hideControlsTimer?.cancel();
    _hideIndicatorTimer?.cancel();
    _timeTimer?.cancel();

    // 释放所有信号
    showControls.dispose();
    showProgressIndicator.dispose();
    activeIndicator.dispose();
    indicatorValue.dispose();
    currentVolume.dispose();
    currentBrightness.dispose();
    progressIndicatorText.dispose();
  }
}

/// 亮度控制服务
class BrightnessVolumeService {
  static final _log = Logger('BrightnessVolumeService');
  static double _currentBrightness = 0.5;
  static double _systemBrightness = 0.5;

  /// 获取当前亮度
  static double get currentBrightness => _currentBrightness;

  /// 设置亮度
  static Future<void> setBrightness(double brightness) async {
    brightness = brightness.clamp(0.0, 1.0);
    _currentBrightness = brightness;

    try {
      await ScreenBrightness().setApplicationScreenBrightness(brightness);
    } catch (e) {
      _log.error('setBrightness', '设置亮度失败', error: e);
    }
  }

  /// 重置为系统亮度
  static Future<void> resetToSystemBrightness() async {
    try {
      await ScreenBrightness().resetApplicationScreenBrightness();
      _currentBrightness = _systemBrightness;
    } catch (e) {
      _log.error('resetToSystemBrightness', '重置亮度失败', error: e);
    }
  }

  static Future<void> initialize() async {
    try {
      _systemBrightness = await ScreenBrightness().system;
      _currentBrightness = await ScreenBrightness().application;
      _currentVolume = await FlutterVolumeController.getVolume() ?? 0.5;

      FlutterVolumeController.addListener((volume) {
        _currentVolume = volume;
      });
    } catch (e) {
      _log.error('initialize', '初始化亮度音量服务失败', error: e);
      _systemBrightness = 0.5;
      _currentBrightness = 0.5;
    }
  }

  static double _currentVolume = 0.5;

  /// 获取当前音量
  static double get currentVolume => _currentVolume;

  /// 设置音量
  static Future<void> setVolume(double volume) async {
    volume = volume.clamp(0.0, 1.0);
    _currentVolume = volume;

    try {
      await FlutterVolumeController.setVolume(volume);
    } catch (e) {
      _log.error('setVolume', '设置音量失败', error: e);
    }
  }

  /// 释放资源
  static void dispose() {
    resetToSystemBrightness();
    FlutterVolumeController.removeListener();
  }
}
