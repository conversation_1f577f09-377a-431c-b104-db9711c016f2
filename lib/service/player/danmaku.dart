import 'dart:async';
import 'dart:io';

import 'package:canvas_danmaku/danmaku_controller.dart';
import 'package:canvas_danmaku/models/danmaku_content_item.dart';
import 'package:fldanplay/model/danmaku.dart';
import 'package:fldanplay/model/video_info.dart';
import 'package:fldanplay/service/configure.dart';
import 'package:fldanplay/service/player/global.dart';
import 'package:fldanplay/utils/danmaku_api_utils.dart';
import 'package:fldanplay/utils/log.dart';
import 'package:get_it/get_it.dart';
import 'package:path_provider/path_provider.dart';
import 'package:signals_flutter/signals_flutter.dart';
import '../../model/history.dart';

class DanmakuService {
  late DanmakuController controller;
  final VideoInfo videoInfo;

  DanmakuService(this.videoInfo);

  ConfigureService configureService = GetIt.I.get<ConfigureService>();
  GlobalPlayerService globalPlayerService = GetIt.I.get<GlobalPlayerService>();

  // 日志器
  final _log = Logger('DanmakuService');
  // 弹幕相关信号
  Map<int, List<Danmaku>> _danmakus = {};
  final Signal<DanmakuSettings> _danmakuSettings = Signal(DanmakuSettings());
  final Signal<bool> danmakuEnabled = Signal(true);
  late History history;
  int episodeId = 0;
  int animeId = 0;
  int lastTime = 0;

  /// 弹幕设置
  ReadonlySignal<DanmakuSettings> get danmakuSettings =>
      _danmakuSettings.readonly();

  Future<void> init() async {
    final sittings = configureService.getDanmakuSettings();
    _danmakuSettings.value = sittings;
    controller.updateOption(sittings.toDanmakuOption());
    globalPlayerService.videoName = videoInfo.videoName;
    loadDanmaku();
  }

  void syncWithVideo(bool isPlaying) {
    if (isPlaying) {
      controller.resume();
    } else {
      controller.pause();
    }
  }

  void clear() {
    controller.clear();
    _danmakus = {};
  }

  /// 重置弹幕位置，用于seek操作后重新开始弹幕显示
  void resetDanmakuPosition() {
    lastTime = 0;
  }

  void setSpeed(double speed) {
    controller.updateOption(
      _danmakuSettings.value
          .copyWith(duration: _danmakuSettings.value.duration / speed)
          .toDanmakuOption(),
    );
  }

  /// 根据当前播放位置更新弹幕显示
  void updatePlayPosition(Duration position, double speed) {
    if (_danmakus.isEmpty) return;
    final currentSecond = position.inSeconds;
    if (lastTime == currentSecond) return;
    lastTime = currentSecond;
    for (Danmaku danmaku in _danmakus[currentSecond] ?? []) {
      // 数据源筛选
      switch (danmaku.source) {
        case 'BiliBili':
          if (!_danmakuSettings.value.bilibiliSource) continue;
          break;
        case 'Gamer':
          if (!_danmakuSettings.value.gamerSource) continue;
          break;
        case 'DanDanPlay':
          if (!_danmakuSettings.value.dandanSource) continue;
          break;
        default:
          if (!_danmakuSettings.value.otherSource) continue;
      }
      var delay = 0;
      if (danmaku.time > position) {
        delay =
            (danmaku.time.inMilliseconds - position.inMilliseconds) ~/ speed;
      }
      Future.delayed(
        Duration(milliseconds: delay),
        () => _addDanmakuToController(danmaku),
      );
    }
  }

  /// 将弹幕添加到控制器中显示
  void _addDanmakuToController(Danmaku danmaku) {
    try {
      // 根据弹幕类型转换为canvas_danmaku的类型
      DanmakuItemType danmakuType;
      switch (danmaku.type) {
        case 4:
          danmakuType = DanmakuItemType.bottom; // 底部弹幕
          break;
        case 5:
          danmakuType = DanmakuItemType.top; // 顶部弹幕
          break;
        default:
          danmakuType = DanmakuItemType.scroll; // 默认滚动弹幕
      }

      // 调用controller的addDanmaku方法
      controller.addDanmaku(
        DanmakuContentItem(
          danmaku.text,
          type: danmakuType,
          color: danmaku.color,
        ),
      );
    } catch (e) {
      _log.error('_addDanmakuToController', '添加弹幕失败', error: e);
    }
  }

  void _danmaku2Map(List<Danmaku> danmakus) {
    _danmakus = {};
    for (var danmaku in danmakus) {
      final key = danmaku.time.inSeconds;
      if (!_danmakus.containsKey(key)) {
        _danmakus[key] = [];
      }
      _danmakus[key]!.add(danmaku);
    }
  }

  void updateDanmakuRatio(List<Danmaku> danmakus) {
    var bili = 0;
    var gamer = 0;
    var dandan = 0;
    var other = 0;
    for (var danmaku in danmakus) {
      switch (danmaku.source) {
        case 'BiliBili':
          bili++;
          break;
        case 'Gamer':
          gamer++;
          break;
        case 'DanDanPlay':
          dandan++;
          break;
        default:
          other++;
      }
    }
    globalPlayerService.danmakuRatio.value = {
      'BiliBili': bili / danmakus.length * 100,
      'Gamer': gamer / danmakus.length * 100,
      'DanDanPlay': dandan / danmakus.length * 100,
      'Other': other / danmakus.length * 100,
    };
  }

  /// 加载弹幕
  Future<void> loadDanmaku({bool force = false}) async {
    try {
      if (!force) {
        final cachedDanmakus = await _getCachedDanmakus(videoInfo.uniqueKey);
        if (cachedDanmakus.isNotEmpty) {
          // 按时间排序弹幕
          cachedDanmakus.sort((a, b) => a.time.compareTo(b.time));
          controller.clear();
          updateDanmakuRatio(cachedDanmakus);
          _danmaku2Map(cachedDanmakus);
          globalPlayerService.showNotification(
            '从缓存加载弹幕: ${cachedDanmakus.length}条',
          );
          return;
        }
      }
      // 从videoName尝试获取
      final animes = await searchEpisodes(videoInfo.videoName);
      if (animes.isEmpty) return;
      final episodes = animes.first.episodes;
      if (episodes.isEmpty) return;
      final episode = episodes.first;
      selectEpisodeAndLoadDanmaku(animes.first.animeId, episode.episodeId);
      // final comments = await DanmakuApiUtils.getComments(episodeId);
      // final danmakus = comments.map((comment) => comment.toDanmaku()).toList();

      // danmakus.sort((a, b) => a.time.compareTo(b.time));
      // // 3. 保存到本地缓存
      // if (danmakus.isNotEmpty) {
      //   await _saveDanmakus(virtualVideoPath, danmakus, episodeId, animeId);
      // }
      // _danmakus = danmakus;
      // _currentDanmakuIndex = 0; // 重置弹幕索引
      // controller.clear();
      // globalPlayerService.showNotification('从API加载弹幕: ${danmakus.length}条');
    } catch (e) {
      _log.error('loadDanmaku', '加载弹幕失败', error: e);
      // 加载失败时设置空列表，避免界面异常
      _danmakus = {};
    }
  }

  /// 从缓存获取弹幕数据
  Future<List<Danmaku>> _getCachedDanmakus(String uniqueKey) async {
    try {
      final documentsDir = await getApplicationDocumentsDirectory();
      final cacheDir = Directory('${documentsDir.path}/danmaku');
      final danmakuFile = File('${cacheDir.path}/$uniqueKey.json');

      if (!await danmakuFile.exists()) {
        return [];
      }

      // 读取并解析弹幕文件
      final jsonString = await danmakuFile.readAsString();
      final danmakuData = DanmakuFile.fromJsonString(jsonString);

      // 检查过期时间
      final expireTime = danmakuData.expireTime.millisecondsSinceEpoch;
      final now = DateTime.now().millisecondsSinceEpoch;
      if (now > expireTime) {
        _log.info('_getCachedDanmakus', '弹幕缓存已过期');
        try {
          final comments = await DanmakuApiUtils.getComments(
            episodeId,
            sc: configureService.autoLanguage.value,
          );
          final danmakus =
              comments.map((comment) => comment.toDanmaku()).toList();
          danmakus.sort((a, b) => a.time.compareTo(b.time));
          if (danmakus.isNotEmpty) {
            await _saveDanmakus(uniqueKey, danmakus, episodeId, animeId);
          }
          _log.info('_getCachedDanmakus', '弹幕缓存已过期，已自动刷新: ${danmakus.length}条');
          return danmakus;
        } catch (e) {
          _log.error('_getCachedDanmakus', '刷新弹幕失败', error: e);
          rethrow;
        }
      }
      episodeId = danmakuData.episodeId;
      animeId = danmakuData.animeId;
      // 解析弹幕数据
      return danmakuData.danmakus;
    } catch (e) {
      _log.warn('_getCachedDanmakus', '读取缓存弹幕失败', error: e);
      return [];
    }
  }

  /// 保存弹幕数据
  Future<void> _saveDanmakus(
    String uniqueKey,
    List<Danmaku> danmakus,
    int episodeId,
    int animeId,
  ) async {
    try {
      // 生成缓存文件路径
      final documentsDir = await getApplicationDocumentsDirectory();
      final cacheDir = Directory('${documentsDir.path}/danmaku');
      if (!await cacheDir.exists()) {
        await cacheDir.create(recursive: true);
      }

      final cacheFile = File('${cacheDir.path}/$uniqueKey.json');

      // 添加缓存元数据
      final cacheData = DanmakuFile(
        uniqueKey: uniqueKey,
        cacheTime: DateTime.now(),
        expireTime: DateTime.now().add(
          danmakus.length > 100
              ? const Duration(days: 3)
              : const Duration(days: 1),
        ),
        danmakus: danmakus,
        episodeId: episodeId,
        animeId: animeId,
      );

      await cacheFile.writeAsString(cacheData.toJsonString());

      _log.info(
        '_saveDanmakuCache',
        '弹幕缓存保存成功: ${cacheFile.path}, 弹幕数量: ${danmakus.length}',
      );
    } catch (e) {
      _log.warn('_saveDanmakuCache', '保存弹幕缓存失败', error: e);
    }
  }

  /// 搜索番剧集数
  Future<List<Anime>> searchEpisodes(String animeName) async {
    try {
      final animes = await DanmakuApiUtils.searchEpisodes(animeName);

      return animes;
    } catch (e) {
      _log.error('searchEpisodes', '搜索番剧失败', error: e);
      rethrow;
    }
  }

  /// 选择episodeId并加载弹幕
  Future<void> selectEpisodeAndLoadDanmaku(int animeId, int episodeId) async {
    try {
      // 获取弹幕
      final comments = await DanmakuApiUtils.getComments(
        episodeId,
        sc: configureService.autoLanguage.value,
      );

      // 转换为内部弹幕格式并排序
      final danmakus = comments.map((comment) => comment.toDanmaku()).toList();
      danmakus.sort((a, b) => a.time.compareTo(b.time));

      // 保存到缓存
      if (danmakus.isNotEmpty) {
        await _saveDanmakus(videoInfo.uniqueKey, danmakus, episodeId, animeId);
      }

      // 更新弹幕列表
      clear();
      updateDanmakuRatio(danmakus);
      _danmaku2Map(danmakus);
      _log.info('selectEpisodeAndLoadDanmaku', '搜索弹幕加载成功: ${danmakus.length}条');
      globalPlayerService.showNotification('从API加载弹幕: ${danmakus.length}条');
    } catch (e) {
      _log.error('selectEpisodeAndLoadDanmaku', '手动选择弹幕加载失败', error: e);
      rethrow;
    }
  }

  /// 更新弹幕设置
  void updateDanmakuSettings(DanmakuSettings settings) {
    _danmakuSettings.value = settings;
    configureService.setDanmakuSettings(settings);
    controller.updateOption(settings.toDanmakuOption());
    _log.debug('updateDanmakuSettings', '弹幕设置已更新: $settings');
  }

  void dispose() {
    _danmakuSettings.dispose();
  }
}
