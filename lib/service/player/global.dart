import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:get_it/get_it.dart';
import 'package:signals/signals_core.dart';

class GlobalPlayerService {
  String videoName = '';
  // 弹幕比例
  // Map<String, double> danmakuRatio = {
  //   'BiliBili': 0,
  //   'Gamer': 0,
  //   'DanDanPlay': 0,
  //   'Other': 0,
  // };
  final Signal<Map<String, double>> danmakuRatio = signal({
    'BiliBili': 0,
    'Gamer': 0,
    'DanDanPlay': 0,
    'Other': 0,
  });

  late BuildContext notificationContext;
  Function() updateListener = () {};

  static Future<void> register() async {
    final service = GlobalPlayerService();
    GetIt.I.registerSingleton<GlobalPlayerService>(service);
  }

  void showNotification(String message) {
    showRawFToast(
      context: notificationContext,
      alignment: FToastAlignment.bottomLeft,
      duration: Duration(seconds: 3),
      builder: (context, entry) {
        return Container(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 14),
          decoration: BoxDecoration(
            color: Colors.grey.shade900.withValues(alpha: 0.7),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(message, style: TextStyle(color: Colors.white)),
        );
      },
    );
  }
}
